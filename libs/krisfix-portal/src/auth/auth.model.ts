import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsEnum } from 'class-validator';

export class RegisterRequest {
  @IsEmail()
  email!: string;

  @IsString()
  @MinLength(6)
  password!: string;

  @IsOptional()
  @IsString()
  first_name?: string;

  @IsOptional()
  @IsString()
  last_name?: string;
}

export class LoginRequest {
  @IsEmail()
  email!: string;

  @IsString()
  password!: string;
}

export class RefreshTokenRequest {
  @IsString()
  refresh_token!: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
}

export type UserRole = 'super_admin' | 'admin' | 'user';

export interface AuthUser {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  role: UserRole;
}

export interface AuthResponse extends AuthTokens {
  user: AuthUser;
}

export interface RefreshTokenResponse {
  access_token: string;
}

export class UpdateUserRoleRequest {
  @IsString()
  user_id!: string;

  @IsEnum(['admin', 'user'])
  role!: 'admin' | 'user';
}

export interface UserDetail {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  role: UserRole;
  is_active: boolean;
  email_verified: boolean;
  created_at: Date;
  updated_at: Date;
}