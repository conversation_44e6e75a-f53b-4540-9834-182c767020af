{"name": "krisfix-portal-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8000", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@krisfix-portal/shared": "file:../libs/krisfix-portal", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "next": "15.5.3", "next-intl": "^4.3.9", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.63.0", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "zod": "^4.1.11"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}