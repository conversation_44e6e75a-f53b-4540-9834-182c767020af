import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import {apiClient} from '@/lib/api-client';

// Generic GET hook
export function useApi<T>(endpoint: string | null, options?: object) {
    return useSWR<T>(endpoint, options);
}

// POST mutation hook
export function useApiPost<T, TArg = unknown>(endpoint: string) {
    return useSWRMutation(
        endpoint,
        async (url: string, {arg}: { arg: TArg }) => {
            return apiClient.post<T>(url, arg);
        }
    );
}

// PUT mutation hook
export function useApiPut<T, TArg = unknown>(endpoint: string) {
    return useSWRMutation(
        endpoint,
        async (url: string, {arg}: { arg: TArg }) => {
            return apiClient.put<T>(url, arg);
        }
    );
}

// PATCH mutation hook
export function useApiPatch<T, TArg = unknown>(endpoint: string) {
    return useSWRMutation(
        endpoint,
        async (url: string, {arg}: { arg: TArg }) => {
            return apiClient.patch<T>(url, arg);
        }
    );
}

// DELETE mutation hook
export function useApiDelete<T>(endpoint: string) {
    return useSWRMutation(
        endpoint,
        async (url: string) => {
            return apiClient.delete<T>(url);
        }
    );
}

// Specific API hooks (examples)
export function useUsers() {
    return useApi('/v1/users');
}

export function useUser(id: string | null) {
    return useApi(id ? `/v1/users/${id}` : null);
}
