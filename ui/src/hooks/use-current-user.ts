import useSWR from 'swr';
import {apiClient} from '@/lib/api-client';
import {authClient} from '@/lib/auth-client';
import {AuthUser} from "@krisfix-portal/shared/auth";

export function useCurrentUser() {
    const {data, error, isLoading, mutate} = useSWR<AuthUser>(
        '/v1/users/me', // Always try to fetch
        async (url) => {
            console.log('Fetching /v1/users/me...');
            try {
                // Try to initialize token first if we don't have one
                if (!authClient.hasAccessToken()) {
                    console.log('No access token, trying to initialize from refresh token...');
                    const initialized = await authClient.initializeToken();
                    if (!initialized) {
                        throw new Error('No valid refresh token');
                    }
                }

                const result = await apiClient.get<AuthUser>(url);
                console.log('/v1/users/me response:', result);
                return result;
            } catch (err) {
                console.error('/v1/users/me error:', err);
                throw err;
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            shouldRetryOnError: false,
        }
    );

    // Debug logging
    if (typeof window !== 'undefined') {
        console.log('useCurrentUser debug:', {
            data,
            error,
            isLoading,
            hasAccessToken: !!authClient.hasAccessToken(),
            isAuthenticated: !!data && !error
        });
    }

    return {
        user: data,
        isLoading,
        isError: error,
        // User is authenticated if we have user data and no error
        isAuthenticated: !!data && !error,
        mutate,
    };
}
