import useSWR from 'swr';
import {apiClient} from '@/lib/api-client';
import {authClient} from '@/lib/auth-client';
import {AuthUser} from "@krisfix-portal/shared/auth";

export function useCurrentUser() {
    const shouldFetch = authClient.isAuthenticated();

    const {data, error, isLoading, mutate} = useSWR<AuthUser>(
        shouldFetch ? '/v1/users/me' : null,
        async (url) => {
            try {
                if (!authClient.hasAccessToken()) {
                    const initialized = await authClient.initializeToken();
                    if (!initialized) {
                        throw new Error('No valid refresh token');
                    }
                }

                return await apiClient.get<AuthUser>(url);
            } catch (err) {
                throw err;
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            shouldRetryOnError: false,
        }
    );

    return {
        user: data,
        isLoading,
        isError: error,
        isAuthenticated: !!data && !error,
        mutate,
    };
}
