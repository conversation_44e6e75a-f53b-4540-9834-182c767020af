import useSWR from 'swr';
import { AuthUser } from '@/types/auth';
import { apiClient } from '@/lib/api-client';
import { authClient } from '@/lib/auth-client';

export function useCurrentUser() {
  const { data, error, isLoading, mutate } = useSWR<AuthUser>(
    '/v1/users/me', // Always try to fetch - server will handle authentication via cookies
    async (url) => {
      console.log('Fetching /v1/users/me...');
      try {
        const result = await apiClient.get<AuthUser>(url);
        console.log('/v1/users/me response:', result);
        return result;
      } catch (err) {
        console.error('/v1/users/me error:', err);
        throw err;
      }
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      shouldRetryOnError: false,
    }
  );

  // Debug logging
  if (typeof window !== 'undefined') {
    console.log('useCurrentUser debug:', {
      data,
      error,
      isLoading,
      isAuthenticated: !!data && !error
    });
  }

  return {
    user: data,
    isLoading,
    isError: error,
    // User is authenticated if we have user data and no error
    isAuthenticated: !!data && !error,
    mutate,
  };
}
