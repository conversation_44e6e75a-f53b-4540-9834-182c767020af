'use client'

import { createContext, useContext, ReactNode, useCallback } from 'react'
import { AuthResponse, AuthUser } from '@krisfix-portal/shared/auth'
import { authClient } from '@/lib/auth-client'
import { useCurrentUser } from '@/hooks/use-current-user'
import { mutate } from 'swr'

interface AuthContextType {
  user: AuthUser | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | null>(null)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { user, isLoading, isAuthenticated, mutate: mutateUser } = useCurrentUser()

  const login = useCallback(async (email: string, password: string) => {
    const response: AuthResponse = await authClient.login(email, password)
    console.log('Login successful, got response:', response)

    // Update SWR cache with user data from response
    await mutateUser(response.user, false)
  }, [mutateUser])

  const logout = useCallback(async () => {
    try {
      await authClient.logout()
    } finally {
      // Clear user data regardless of API call success
      await mutateUser(undefined, false)
      // Clear all SWR cache
      await mutate(() => true, undefined, { revalidate: false })
    }
  }, [mutateUser])

  const refreshUser = useCallback(async () => {
    await mutateUser()
  }, [mutateUser])

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
