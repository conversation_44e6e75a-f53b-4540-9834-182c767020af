'use client'

import {usePathname, useRouter} from 'next/navigation'
import {useLocale} from 'next-intl'
import {Button} from '@/components/ui/button'

export function LanguageSwitcher() {
    const router = useRouter()
    const pathname = usePathname()
    const locale = useLocale()

    const switchLanguage = (newLocale: string) => {
        // Replace current locale in pathname with new locale
        const newPathname = pathname.replace(`/${locale}`, `/${newLocale}`)
        router.push(newPathname)
    }

    return (
        <div className="flex gap-2">
            <Button
                variant={locale === 'en' ? 'default' : 'outline'}
                size="sm"
                onClick={() => switchLanguage('en')}
            >
                EN
            </Button>
            <Button
                variant={locale === 'de' ? 'default' : 'outline'}
                size="sm"
                onClick={() => switchLanguage('de')}
            >
                DE
            </Button>
        </div>
    )
}
