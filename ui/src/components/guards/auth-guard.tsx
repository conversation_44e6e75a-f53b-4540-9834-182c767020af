'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
}

export function AuthGuard({ children, requireAuth = true }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isClient, setIsClient] = useState(false)

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    console.log('AuthGuard effect:', { isClient, isLoading, isAuthenticated, requireAuth, pathname });

    // Only redirect after loading is complete AND we're on the client
    if (isClient && !isLoading) {
      // Extract locale from pathname (e.g., /en/login -> en)
      const locale = pathname.split('/')[1]

      if (requireAuth && !isAuthenticated) {
        console.log('Redirecting to login - protected route, not authenticated');
        // Protected route - redirect to login if not authenticated
        router.push(`/${locale}/login`)
      } else if (!requireAuth && isAuthenticated) {
        console.log('Redirecting to dashboard - public route, already authenticated');
        // Public route (like login) - redirect to dashboard if already authenticated
        router.push(`/${locale}/dashboard`)
      }
    }
  }, [isClient, isAuthenticated, isLoading, requireAuth, router, pathname])

  // During SSR or initial client render, always show loading
  if (!isClient || isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  // After client hydration, check authentication
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!requireAuth && isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  return <>{children}</>
}
