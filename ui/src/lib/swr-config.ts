import { SWRConfiguration } from 'swr';
import { apiClient } from './api-client';

// Default SWR fetcher using our API client
export const fetcher = async (url: string) => {
  return apiClient.get(url);
};

// Global SWR configuration
export const swrConfig: SWRConfiguration = {
  fetcher,
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  errorRetryCount: 3,
  errorRetryInterval: 1000,
  dedupingInterval: 2000,
  // Cache for 5 minutes by default
  focusThrottleInterval: 300000,
};