import {authClient} from './auth-client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export class ApiClient {
    private readonly baseURL: string;

    constructor(baseURL: string = API_BASE_URL) {
        this.baseURL = baseURL;
    }

    // HTTP methods
    async get<T>(endpoint: string): Promise<T> {
        return this.request<T>(endpoint, {method: 'GET'});
    }

    async post<T>(endpoint: string, data?: unknown): Promise<T> {
        return this.request<T>(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    async put<T>(endpoint: string, data?: unknown): Promise<T> {
        return this.request<T>(endpoint, {
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    async patch<T>(endpoint: string, data?: unknown): Promise<T> {
        return this.request<T>(endpoint, {
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    async delete<T>(endpoint: string): Promise<T> {
        return this.request<T>(endpoint, {method: 'DELETE'});
    }

    private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
        const url = `${this.baseURL}${endpoint}`;

        const headers: HeadersInit = {
            'Content-Type': 'application/json',
            ...options.headers,
        };

        // No need to add auth token - server handles authentication via cookies

        const config: RequestInit = {
            ...options,
            headers,
            credentials: 'include', // Include cookies in requests
        };

        try {
            const response = await fetch(url, config);

            // Handle 401 - try to refresh token
            if (response.status === 401) {
                const refreshed = await authClient.refreshAccessToken();
                if (refreshed) {
                    // Retry request - server will handle authentication via cookies
                    const retryResponse = await fetch(url, config);
                    if (!retryResponse.ok) {
                        throw new Error(`API Error: ${retryResponse.status} ${retryResponse.statusText}`);
                    }
                    return retryResponse.json();
                }
                // If refresh failed, logout user
                await authClient.logout();
                throw new Error('Authentication failed');
            }

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }

            return response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
}

export const apiClient = new ApiClient();
