import { AuthResponse, RefreshTokenResponse } from '@krisfix-portal/shared/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

class AuthClient {
  private accessToken: string | null = null;

  constructor() {
    if (typeof window !== 'undefined') {
      this.accessToken = localStorage.getItem('access_token');
    }
  }

  private async apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.accessToken && !(options.headers as Record<string, string>)?.Authorization) {
      (headers as Record<string, string>).Authorization = `Bearer ${this.accessToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include', // Include cookies in requests
    });

    if (!response.ok) {
      if (response.status === 401) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          return this.apiCall(endpoint, options);
        }
      }
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.apiCall<Omit<AuthResponse, 'refresh_token'>>('/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    this.setAccessToken(response.access_token);
    // Return response with dummy refresh_token for compatibility
    return { ...response, refresh_token: '' };
  }

  async register(email: string, password: string, firstName?: string, lastName?: string): Promise<AuthResponse> {
    const response = await this.apiCall<Omit<AuthResponse, 'refresh_token'>>('/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        first_name: firstName,
        last_name: lastName,
      }),
    });

    this.setAccessToken(response.access_token);
    // Return response with dummy refresh_token for compatibility
    return { ...response, refresh_token: '' };
  }

  async refreshAccessToken(): Promise<boolean> {
    try {
      const response = await this.apiCall<RefreshTokenResponse>('/v1/auth/refresh', {
        method: 'POST',
        headers: {},
      });

      this.accessToken = response.access_token;
      if (typeof window !== 'undefined') {
        localStorage.setItem('access_token', this.accessToken);
      }
      return true;
    } catch {
      this.logout();
      return false;
    }
  }

  async logout(): Promise<void> {
    try {
      // Make logout call directly without going through apiCall to avoid refresh token loop
      const url = `${API_BASE_URL}/v1/auth/logout`;
      await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
        },
        credentials: 'include', // Include cookies
      });
    } catch {
      // Ignore errors during logout
    }

    this.clearTokens();
  }

  private setAccessToken(accessToken: string): void {
    this.accessToken = accessToken;

    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', accessToken);
    }
  }

  private clearTokens(): void {
    this.accessToken = null;

    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      // Remove old refresh_token from localStorage if it exists
      localStorage.removeItem('refresh_token');
    }
  }

  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Helper method for making authenticated API calls
  async authenticatedRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    return this.apiCall<T>(endpoint, options);
  }
}

export const authClient = new AuthClient();
