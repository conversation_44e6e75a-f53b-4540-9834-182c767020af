import { AuthResponse, RefreshTokenResponse } from '@krisfix-portal/shared/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

class AuthClient {
  // No local storage - everything handled server-side via cookies
  constructor() {
    // No initialization needed
  }

  private async apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include', // Include cookies in requests
    });

    if (!response.ok) {
      if (response.status === 401) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          return this.apiCall(endpoint, options);
        }
      }
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.apiCall<Omit<AuthResponse, 'refresh_token'>>('/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    // No local storage - return response with dummy refresh_token for compatibility
    return { ...response, refresh_token: '' };
  }

  async register(email: string, password: string, firstName?: string, lastName?: string): Promise<AuthResponse> {
    const response = await this.apiCall<Omit<AuthResponse, 'refresh_token'>>('/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        first_name: firstName,
        last_name: lastName,
      }),
    });

    // No local storage - return response with dummy refresh_token for compatibility
    return { ...response, refresh_token: '' };
  }

  async refreshAccessToken(): Promise<boolean> {
    try {
      await this.apiCall<RefreshTokenResponse>('/v1/auth/refresh', {
        method: 'POST',
        headers: {},
      });

      // No local storage - server handles everything via cookies
      return true;
    } catch {
      this.logout();
      return false;
    }
  }

  async logout(): Promise<void> {
    try {
      const url = `${API_BASE_URL}/v1/auth/logout`;
      await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies
      });
    } catch {
      // Ignore errors during logout
    }

    // Clear any remaining localStorage items from old implementation
    this.clearOldLocalStorage();
  }

  private clearOldLocalStorage(): void {
    if (typeof window !== 'undefined') {
      // Clean up any old localStorage items
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('cached_user');
    }
  }

  // Check authentication status by making a request to the server
  async isAuthenticated(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/v1/users/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  // Legacy method for compatibility - always returns null since we don't store tokens locally
  getAccessToken(): string | null {
    return null;
  }

  // Helper method for making authenticated API calls
  async authenticatedRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    return this.apiCall<T>(endpoint, options);
  }
}

export const authClient = new AuthClient();
