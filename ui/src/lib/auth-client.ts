import { AuthResponse, RefreshTokenResponse } from '@krisfix-portal/shared/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

class AuthClient {
  private accessToken: string | null = null;
  private isRefreshing: boolean = false;

  constructor() {
    // Access token will be loaded from refresh token on first request
  }

  private async apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add access token if available
    if (this.accessToken && !(options.headers as Record<string, string>)?.Authorization) {
      (headers as Record<string, string>).Authorization = `Bearer ${this.accessToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include', // Include cookies in requests
    });

    if (!response.ok) {
      if (response.status === 401 && !this.isRefreshing) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          return this.apiCall(endpoint, options);
        }
      }
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    console.log('AuthClient.login called with:', email);

    const response = await this.apiCall<AuthResponse>('/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    console.log('Login response:', response);

    // Store access token in memory
    this.accessToken = response.access_token;
    console.log('Access token stored:', !!this.accessToken);

    return response;
  }

  async register(email: string, password: string, firstName?: string, lastName?: string): Promise<AuthResponse> {
    const response = await this.apiCall<AuthResponse>('/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        first_name: firstName,
        last_name: lastName,
      }),
    });

    // Store access token in memory
    this.accessToken = response.access_token;
    return response;
  }

  async refreshAccessToken(): Promise<boolean> {
    if (this.isRefreshing) {
      return false; // Already refreshing, prevent multiple calls
    }

    this.isRefreshing = true;

    try {
      // Make direct fetch call to avoid infinite loop
      const response = await fetch(`${API_BASE_URL}/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Refresh failed: ${response.status}`);
      }

      const data = await response.json();

      // Store new access token in memory
      this.accessToken = data.access_token;
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.logout();
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  async logout(): Promise<void> {
    try {
      const url = `${API_BASE_URL}/v1/auth/logout`;
      await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
        },
        credentials: 'include', // Include cookies
      });
    } catch {
      // Ignore errors during logout
    }

    // Clear access token from memory
    this.accessToken = null;

    // Clear any remaining localStorage items from old implementation
    this.clearOldLocalStorage();
  }

  private clearOldLocalStorage(): void {
    if (typeof window !== 'undefined') {
      // Clean up any old localStorage items
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('cached_user');
    }
  }

  // Initialize access token from refresh token if needed
  async initializeToken(): Promise<boolean> {
    if (this.accessToken) {
      return true; // Already have token
    }

    // Simply try to refresh - if no valid refresh token exists, server will return 401
    console.log('Attempting to initialize token from refresh token...');
    return await this.refreshAccessToken();
  }

  // Check if we have access token in memory
  isAuthenticated(): boolean {
    const hasToken = !!this.accessToken;
    console.log('isAuthenticated check:', { hasToken });
    return hasToken;
  }

  // Check if we have access token in memory only
  hasAccessToken(): boolean {
    return !!this.accessToken;
  }

  // Get access token from memory
  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Helper method for making authenticated API calls
  async authenticatedRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    return this.apiCall<T>(endpoint, options);
  }
}

export const authClient = new AuthClient();
