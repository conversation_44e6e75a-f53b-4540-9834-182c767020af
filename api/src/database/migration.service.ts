import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { sql } from 'drizzle-orm';
import { DatabaseService } from './database.service';
import * as path from 'path';

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);
  private readonly LOCK_ID = 9876543210;
  private readonly MAX_RETRIES = 10;
  private readonly INITIAL_RETRY_DELAY = 1000; // 1 second

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly configService: ConfigService,
  ) {}

  async runMigrations() {
    const shouldRunMigrations = this.configService.get<boolean>(
      'RUN_MIGRATIONS',
      true,
    );

    if (!shouldRunMigrations) {
      this.logger.log('Migrations disabled via RUN_MIGRATIONS=false');
      return;
    }

    const environment = this.configService.get<string>(
      'NODE_ENV',
      'development',
    );

    let retries = 0;
    let delay = this.INITIAL_RETRY_DELAY;

    while (retries < this.MAX_RETRIES) {
      try {
        // Try to acquire advisory lock
        const lockAcquired = await this.acquireLock();

        if (!lockAcquired) {
          this.logger.warn(
            `Failed to acquire migration lock, attempt ${retries + 1}/${this.MAX_RETRIES}. Retrying in ${delay}ms...`,
          );
          await this.sleep(delay);
          delay *= 2; // Exponential backoff
          retries++;
          continue;
        }

        try {
          this.logger.log(
            'Migration lock acquired, starting database migrations...',
          );

          const migrationsPath = path.join(
            process.cwd(),
            'drizzle',
            'migrations',
          );
          this.logger.log(`Looking for migrations in: ${migrationsPath}`);

          await migrate(this.databaseService.db, {
            migrationsFolder: migrationsPath,
          });

          this.logger.log(
            `Database migrations completed successfully in ${environment} mode`,
          );

          return;
        } finally {
          await this.releaseLock();
          this.logger.log('Migration lock released');
        }
      } catch (error) {
        this.logger.error('Database migration failed:', error);

        if (environment === 'production') {
          throw new Error(
            `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }

        // In development, just warn but continue
        this.logger.warn(
          'Continuing despite migration error in development mode',
        );
        return;
      }
    }

    // If we've exhausted all retries
    const errorMsg = `Failed to acquire migration lock after ${this.MAX_RETRIES} attempts`;
    this.logger.error(errorMsg);
    if (environment === 'production') {
      throw new Error(errorMsg);
    }
  }

  private async acquireLock(): Promise<boolean> {
    try {
      // PostgreSQL advisory lock - returns true if lock was acquired, false if not
      const result = await this.databaseService.db.execute<{
        pg_try_advisory_lock: boolean;
      }>(sql`SELECT pg_try_advisory_lock(${this.LOCK_ID})`);
      return result[0].pg_try_advisory_lock;
    } catch (error) {
      this.logger.error('Error acquiring migration lock:', error);
      return false;
    }
  }

  private async releaseLock(): Promise<void> {
    try {
      await this.databaseService.db.execute(
        sql`SELECT pg_advisory_unlock(${this.LOCK_ID})`,
      );
    } catch (error) {
      this.logger.error('Error releasing migration lock:', error);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
