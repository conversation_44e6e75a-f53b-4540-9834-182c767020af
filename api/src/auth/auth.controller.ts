import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Request,
  Response,
  UseGuards,
} from '@nestjs/common';
import type { Response as ExpressResponse } from 'express';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards';
import { Public } from './decorators';
import { RegisterRequest } from '@krisfix-portal/shared/auth';
import { ValidatedUser } from './auth.model';
import { ConfigService } from '@nestjs/config';

@Controller('v1/auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  @Public()
  @Post('register')
  async register(
    @Body() registerRequest: RegisterRequest,
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const result = await this.authService.register(registerRequest);
    this.setRefreshTokenCookie(res, result.refresh_token);

    return result;
  }

  @Public()
  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @Post('login')
  async login(
    @Request() req: { user: ValidatedUser },
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const result = await this.authService.login(req.user);
    this.setRefreshTokenCookie(res, result.refresh_token);

    return result;
  }

  @Public()
  @Post('refresh')
  async refresh(
    @Request() req: { cookies: { refresh_token?: string } },
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const refreshToken = req.cookies.refresh_token;
    if (!refreshToken) {
      throw new BadRequestException('Refresh token not found');
    }

    const result = await this.authService.refreshAccessToken(refreshToken);

    if (result.refresh_token) {
      this.setRefreshTokenCookie(res, result.refresh_token);
    }

    return { access_token: result.access_token };
  }

  @Post('logout')
  async logout(
    @Request() req: { cookies: { refresh_token?: string } },
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const refreshToken = req.cookies.refresh_token;
    if (refreshToken) {
      await this.authService.logout(refreshToken);
    }

    this.clearRefreshTokenCookie(res);
    return { message: 'Logged out successfully' };
  }

  private setRefreshTokenCookie(res: ExpressResponse, refreshToken: string) {
    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: this.configService.get<string>('NODE_ENV') === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/',
    });
  }

  private clearRefreshTokenCookie(res: ExpressResponse) {
    res.clearCookie('refresh_token', {
      httpOnly: true,
      secure: this.configService.get<string>('NODE_ENV') === 'production',
      sameSite: 'strict',
      path: '/',
    });
  }
}
