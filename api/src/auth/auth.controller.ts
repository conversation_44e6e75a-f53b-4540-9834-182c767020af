import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards';
import { Public } from './decorators';
import {
  RefreshTokenRequest,
  RegisterRequest,
} from '@krisfix-portal/shared/auth';
import { ValidatedUser } from './auth.model';

@Controller('v1/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('register')
  async register(@Body() registerRequest: RegisterRequest) {
    return this.authService.register(registerRequest);
  }

  @Public()
  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @Post('login')
  async login(@Request() req: { user: ValidatedUser }) {
    return this.authService.login(req.user);
  }

  @Public()
  @Post('refresh')
  async refresh(@Body() refreshTokenRequest: RefreshTokenRequest) {
    return this.authService.refreshAccessToken(
      refreshTokenRequest.refresh_token,
    );
  }

  @Post('logout')
  async logout(@Body() refreshTokenRequest: RefreshTokenRequest) {
    await this.authService.logout(refreshTokenRequest.refresh_token);
    return { message: 'Logged out successfully' };
  }
}
