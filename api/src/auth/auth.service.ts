import {
  ConflictException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import {
  DatabaseService,
  NewRefreshToken,
  refreshTokens,
  User,
  users,
} from '../database';
import { UsersService } from '../core/users/users.service';
import {
  AuthResponse,
  RefreshTokenResponse,
  RegisterRequest,
} from '@krisfix-portal/shared/auth';
import { jwtConstants } from './constants';
import { JwtPayload, ValidatedUser } from './auth.model';
import { and, eq, gt, lt, sql } from 'drizzle-orm';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly databaseService: DatabaseService,
  ) {}

  async validateUser(
    email: string,
    password: string,
  ): Promise<ValidatedUser | null> {
    const user = await this.usersService.findByEmail(email);

    if (!user || !user.isActive) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

    if (!isPasswordValid) {
      return null;
    }
    return this.toValidatedUser(user);
  }

  async login(user: ValidatedUser): Promise<AuthResponse> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.generateRefreshToken(user.id);

    return this.toAuthResponse(user, accessToken, refreshToken);
  }

  async register(registerRequest: RegisterRequest): Promise<AuthResponse> {
    const emailExists = await this.usersService.existsByEmail(
      registerRequest.email,
    );

    if (emailExists) {
      throw new ConflictException('User with this email already exists');
    }

    // Check if this is the first user - if so, make them super_admin
    const userCount = await this.databaseService.db
      .select({ count: sql<number>`count(*)` })
      .from(users);

    const isFirstUser = +userCount[0].count === 0;
    const role = isFirstUser ? 'super_admin' : 'user';

    const user = await this.usersService.create(
      registerRequest.email,
      registerRequest.password,
      registerRequest.first_name,
      registerRequest.last_name,
      role,
    );

    return this.login(this.toValidatedUser(user));
  }

  async refreshAccessToken(
    refreshToken: string,
  ): Promise<RefreshTokenResponse & { refresh_token?: string }> {
    const tokenRecords = await this.databaseService.db
      .select()
      .from(refreshTokens)
      .where(
        and(
          eq(refreshTokens.token, refreshToken),
          gt(refreshTokens.expiresAt, new Date()),
        ),
      )
      .limit(1);

    if (tokenRecords.length === 0) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    const tokenRecord = tokenRecords[0];
    const user = await this.usersService.findById(tokenRecord.userId);

    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Generate new access token
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);

    // Token rotation: Generate new refresh token and invalidate old one
    const newRefreshToken = await this.rotateRefreshToken(refreshToken, user.id);

    return {
      access_token: accessToken,
      refresh_token: newRefreshToken,
    };
  }

  async logout(refreshToken: string): Promise<void> {
    await this.databaseService.db
      .delete(refreshTokens)
      .where(eq(refreshTokens.token, refreshToken));
  }

  async cleanupExpiredTokens(): Promise<void> {
    await this.databaseService.db
      .delete(refreshTokens)
      .where(lt(refreshTokens.expiresAt, new Date()));

    this.logger.log('Cleaned up expired refresh tokens');
  }

  private toValidatedUser(user: User) {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
    };
  }

  private async generateRefreshToken(userId: string): Promise<string> {
    await this.databaseService.db
      .delete(refreshTokens)
      .where(eq(refreshTokens.userId, userId));

    const token = this.jwtService.sign(
      {
        sub: userId,
        type: 'refresh',
        nonce: Math.random().toString(36),
      },
      {
        expiresIn: '7d',
        secret: this.configService.getOrThrow<string>('JWT_SECRET'),
      },
    );

    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + jwtConstants.refreshTokenExpiry);

    const newRefreshToken: NewRefreshToken = {
      userId,
      token,
      expiresAt,
    };

    await this.databaseService.db.insert(refreshTokens).values(newRefreshToken);

    return token;
  }

  private async rotateRefreshToken(oldToken: string, userId: string): Promise<string> {
    // Delete the old refresh token
    await this.databaseService.db
      .delete(refreshTokens)
      .where(eq(refreshTokens.token, oldToken));

    // Generate new refresh token
    const token = this.jwtService.sign(
      {
        sub: userId,
        type: 'refresh',
        nonce: Math.random().toString(36),
      },
      {
        expiresIn: '7d',
        secret: this.configService.getOrThrow<string>('JWT_SECRET'),
      },
    );

    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + jwtConstants.refreshTokenExpiry);

    const newRefreshToken: NewRefreshToken = {
      userId,
      token,
      expiresAt,
    };

    await this.databaseService.db.insert(refreshTokens).values(newRefreshToken);

    this.logger.log(`Refresh token rotated for user ${userId}`);
    return token;
  }

  private toAuthResponse(
    user: ValidatedUser,
    accessToken: string,
    refreshToken: string,
  ) {
    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        first_name: user.firstName,
        last_name: user.lastName,
        role: user.role,
      },
    };
  }
}
