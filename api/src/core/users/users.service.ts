import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService, NewUser, User, users } from '../../database';
import { eq } from 'drizzle-orm';
import * as bcrypt from 'bcrypt';
import { UserDetail, UserRole } from '@krisfix-portal/shared/auth';

@Injectable()
export class UsersService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.databaseService.db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    return result[0] || null;
  }

  async findById(id: string): Promise<User | null> {
    const result = await this.databaseService.db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    return result[0] || null;
  }

  async create(
    email: string,
    password: string,
    firstName?: string,
    lastName?: string,
    role: UserRole = 'user',
  ): Promise<User> {
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser: NewUser = {
      email,
      passwordHash: hashedPassword,
      firstName: firstName || null,
      lastName: lastName || null,
      role,
    };

    const result = await this.databaseService.db
      .insert(users)
      .values(newUser)
      .returning();

    return result[0];
  }

  async existsByEmail(email: string): Promise<boolean> {
    const result = await this.databaseService.db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    return result.length > 0;
  }

  async findAll(): Promise<User[]> {
    return this.databaseService.db.select().from(users);
  }

  async updateRole(userId: string, role: UserRole): Promise<User> {
    const result = await this.databaseService.db
      .update(users)
      .set({ role })
      .where(eq(users.id, userId))
      .returning();

    return result[0];
  }

  async getAll(): Promise<UserDetail[]> {
    const allUsers = await this.findAll();
    return allUsers.map((user) => this.transformUser(user));
  }

  async get(id: string): Promise<UserDetail> {
    const user = await this.findById(id);
    if (!user) {
      throw new Error('User not found');
    }
    return this.transformUser(user);
  }

  async updateUserRole(
    userId: string,
    role: UserRole,
    currentUserId: string,
  ): Promise<UserDetail> {
    const targetUser = await this.findById(userId);
    if (!targetUser) {
      throw new NotFoundException('User not found');
    }

    if (targetUser.role === 'super_admin') {
      throw new BadRequestException('Cannot change super admin role');
    }

    if (userId === currentUserId) {
      throw new BadRequestException('Cannot change your own role');
    }

    const updatedUser = await this.updateRole(userId, role);
    return this.transformUser(updatedUser);
  }

  private transformUser(user: User): UserDetail {
    return {
      id: user.id,
      email: user.email,
      first_name: user.firstName,
      last_name: user.lastName,
      role: user.role,
      is_active: user.isActive,
      email_verified: user.emailVerified,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    };
  }
}
