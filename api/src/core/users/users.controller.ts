import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Request,
  UseGuards,
} from '@nestjs/common';

import { UsersService } from './users.service';
import { UpdateUserRoleRequest } from '@krisfix-portal/shared/auth';
import { JwtAuthGuard, RolesGuard } from '../../auth/guards';
import { Roles } from '../../auth/decorators';

@Controller('v1/users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  async getCurrentUser(@Request() req: { user: { userId: string } }) {
    return this.usersService.get(req.user.userId);
  }

  @Get()
  @Roles('super_admin', 'admin')
  async getAll() {
    return this.usersService.getAll();
  }

  @Get(':id')
  @Roles('super_admin', 'admin')
  async get(@Param('id') id: string) {
    return this.usersService.get(id);
  }

  @Patch(':id/role')
  @Roles('super_admin')
  async updateUserRole(
    @Param('id') userId: string,
    @Body() updateRoleRequest: UpdateUserRoleRequest,
    @Request() req: { user: { userId: string } },
  ) {
    return this.usersService.updateUserRole(
      userId,
      updateRoleRequest.role,
      req.user.userId,
    );
  }
}
