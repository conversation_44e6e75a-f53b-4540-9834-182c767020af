import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import type { Express } from 'express';
import { AppModule } from './app.module';
import { MigrationService } from './database';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    rawBody: true, // Enable raw body for webhook signature validation
  });

  app.useLogger(app.get(Logger));

  app.useGlobalPipes(new ValidationPipe({}));

  // Disable ETags to prevent 304 responses
  (app.getHttpAdapter().getInstance() as Express).set('etag', false);

  const configService = app.get(ConfigService);

  // Enable CORS
  const allowedOrigins = configService
    .getOrThrow<string>('CORS_ORIGINS')
    .split(',');
  app.enableCors({
    origin: allowedOrigins,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  app.setGlobalPrefix('api');

  const port = configService.get<number>('PORT', 9000);
  const environment = configService.get<string>('NODE_ENV', 'development');

  await app.listen(port);

  const migrationService = app.get(MigrationService);
  await migrationService.runMigrations();

  const logger = app.get(Logger);
  logger.log(
    `Application is running on port ${port} in ${environment} mode`,
    'Bootstrap',
  );
}

void bootstrap();
